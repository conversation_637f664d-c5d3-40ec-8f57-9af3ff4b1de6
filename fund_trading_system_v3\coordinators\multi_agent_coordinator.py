"""
多智能体协调器 V3
负责协调所有智能体的工作，集成传统智能体和增强版决策智能体
"""

import logging
import sys
import os
from datetime import datetime
from collections import deque
from typing import Dict, Any

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from agents.traditional import TechnicalAgent, GuaAnalysisAgent, FundFlowAgent
from agents.enhanced import EnhancedDecisionAgentV3, RiskControlAgent
from core.market_environment_assessor import MarketEnvironmentAssessor
from core.decision_explainer import DecisionExplainer


class MultiAgentCoordinatorV3:
    """
    @class MultiAgentCoordinatorV3
    @brief 多智能体协调器 V3版本
    @details 负责协调所有智能体的工作，集成传统智能体和增强版决策智能体
    """
    
    def __init__(self):
        self.logger = logging.getLogger(f"{self.__class__.__name__}")

        # 初始化传统智能体
        self.technical_agent = TechnicalAgent("TechnicalAgent_V3")
        self.gua_agent = GuaAnalysisAgent("GuaAgent_V3")
        self.flow_agent = FundFlowAgent("FlowAgent_V3")

        # 初始化增强版决策智能体
        self.enhanced_decision_agent = EnhancedDecisionAgentV3("EnhancedDecisionV3")

        # 初始化风控智能体
        self.risk_control_agent = RiskControlAgent()

        # 初始化市场环境评估器
        self.market_assessor = MarketEnvironmentAssessor()

        # 初始化决策解释器
        self.decision_explainer = DecisionExplainer()

        # 协调历史
        self.coordination_history = deque(maxlen=50)
        
    def coordinate_analysis(self, fund_code: str) -> Dict[str, Any]:
        """
        @brief 协调所有智能体进行综合分析
        @param fund_code: 基金代码
        @return: 综合分析结果
        """
        try:
            start_time = datetime.now()
            self.logger.info(f"开始协调分析基金: {fund_code}")
            
            # ===============================
            # 第一阶段：传统智能体数据收集
            # ===============================
            
            # 技术分析
            technical_data = self.technical_agent.process({'fund_code': fund_code})
            
            # 卦象分析
            gua_data = self.gua_agent.process({'fund_code': fund_code})
            
            # 资金流向分析
            flow_data = self.flow_agent.process({'fund_code': fund_code})
            
            # ===============================
            # 第二阶段：数据整合和预处理
            # ===============================
            
            # 整合所有数据
            integrated_data = self._integrate_agent_data(
                fund_code, technical_data, gua_data, flow_data
            )
            
            # ===============================
            # 第三阶段：增强版决策分析
            # ===============================

            # 执行增强版决策分析
            enhanced_result = self.enhanced_decision_agent.process(integrated_data)

            # ===============================
            # 第四阶段：风控验证
            # ===============================

            # 执行风控验证
            risk_validation = self._perform_risk_validation(
                fund_code, enhanced_result, integrated_data
            )

            # ===============================
            # 第五阶段：结果整合和输出
            # ===============================
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # 整合最终结果
            final_result = {
                # 基础信息
                'fund_code': fund_code,
                'analysis_time': start_time.isoformat(),
                'total_processing_time': processing_time,
                'coordinator_version': 'V3.1',  # 更新版本号

                # 传统智能体结果
                'traditional_agents': {
                    'technical_analysis': technical_data,
                    'gua_analysis': gua_data,
                    'flow_analysis': flow_data
                },

                # 增强版决策结果
                'enhanced_decision': enhanced_result,

                # 风控验证结果
                'risk_control': risk_validation,

                # 最终决策（经过风控验证后的决策）
                'final_decision': risk_validation.get('final_decision', 'hold'),
                'final_confidence': risk_validation.get('confidence', 0.0),

                # 协调摘要
                'coordination_summary': self._generate_coordination_summary_v2(
                    technical_data, gua_data, flow_data, enhanced_result, risk_validation
                )
            }
            
            # 记录协调历史
            self._record_coordination(final_result)
            
            self.logger.info(f"基金 {fund_code} 分析完成，耗时 {processing_time:.3f} 秒")
            
            return final_result
            
        except Exception as e:
            self.logger.error(f"协调分析失败 {fund_code}: {str(e)}")
            return {
                'fund_code': fund_code,
                'error': str(e),
                'analysis_time': datetime.now().isoformat(),
                'coordinator_version': 'V3.1',
                'final_decision': 'hold',  # 出错时保守决策
                'final_confidence': 0.0
            }

    def _perform_risk_validation(self, fund_code: str, enhanced_result: Dict[str, Any],
                               integrated_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        @brief 执行风控验证
        @param fund_code: 基金代码
        @param enhanced_result: 增强版决策结果
        @param integrated_data: 整合数据
        @return: 风控验证结果
        """
        try:
            # 获取建议决策
            proposed_decision = enhanced_result.get('decision', 'hold')

            # 准备风控验证数据
            risk_data = {
                'fund_code': fund_code,
                'analysis_result': integrated_data,
                'proposed_decision': proposed_decision
            }

            # 执行风控验证
            risk_result = self.risk_control_agent.process(risk_data)

            # 生成市场环境评估
            dimension_evaluations = integrated_data.get('dimension_evaluations', {})
            market_assessment = self.market_assessor.assess_market_environment(dimension_evaluations, fund_code)

            # 生成决策解释
            decision_explanation = self.decision_explainer.explain_risk_decision(
                risk_result.get('risk_validation', {}),
                integrated_data.get('technical_analysis', {}),
                {'market_assessment': market_assessment}
            )

            # 整合风控结果
            final_risk_result = {
                'risk_validation_result': risk_result,
                'market_environment_assessment': market_assessment,
                'decision_explanation': decision_explanation,
                'final_decision': risk_result.get('final_decision', 'hold'),
                'confidence': risk_result.get('risk_validation', {}).get('confidence', 0.0),
                'risk_level': risk_result.get('risk_level', 'unknown')
            }

            self.logger.info(f"风控验证完成 - {fund_code}: {final_risk_result['final_decision']}")

            return final_risk_result

        except Exception as e:
            self.logger.error(f"风控验证失败 {fund_code}: {str(e)}")
            return {
                'error': str(e),
                'final_decision': 'hold',  # 出错时保守决策
                'confidence': 0.0,
                'risk_level': 'critical'
            }
    
    def _integrate_agent_data(self, fund_code: str, 
                             technical_data: Dict, 
                             gua_data: Dict, 
                             flow_data: Dict) -> Dict[str, Any]:
        """整合各智能体数据"""
        try:
            # 构建增强版决策智能体需要的数据结构
            integrated_data = {
                'fund_code': fund_code,
                
                # 价格相关数据
                'price_data': flow_data.get('price_data', {}),
                
                # 技术指标数据
                'technical_data': technical_data.get('technical_indicators', {}),
                
                # 卦象数据
                'gua_data': {
                    'gua_score': gua_data.get('gua_score', 0.0),
                    'main_gua': gua_data.get('main_gua', ''),
                    'is_select_gua': gua_data.get('is_select_gua', False),
                    'is_buy_gua': gua_data.get('is_buy_gua', False),
                    'is_sell_gua': gua_data.get('is_sell_gua', False)
                },
                
                # 资金流向数据
                'flow_data': flow_data,
                
                # 成交量分析数据
                'volume_analysis': {
                    'current_volume_ratio': flow_data.get('volume_ratio', 1.0),
                    'volume_level': flow_data.get('volume_level', 'normal')
                },
                
                # 时间框架背驰数据（如果有的话）
                'timeframe_divergence': {
                    'transition_probability': 0.5,  # 默认值
                    'consensus_direction': 'neutral',
                    'transition_signal': 'none'
                }
            }
            
            return integrated_data
            
        except Exception as e:
            self.logger.error(f"数据整合失败: {str(e)}")
            return {'fund_code': fund_code, 'error': str(e)}
    
    def _generate_coordination_summary_v2(self, technical_data: Dict,
                                         gua_data: Dict,
                                         flow_data: Dict,
                                         enhanced_result: Dict,
                                         risk_validation: Dict) -> Dict[str, Any]:
        """生成包含风控验证的协调摘要V2"""
        try:
            # 获取原始协调摘要
            base_summary = self._generate_coordination_summary(
                technical_data, gua_data, flow_data, enhanced_result
            )

            # 添加风控信息
            risk_info = {
                'risk_control_passed': risk_validation.get('risk_validation_result', {}).get('risk_validation', {}).get('passed', False),
                'risk_level': risk_validation.get('risk_level', 'unknown'),
                'final_decision_after_risk_control': risk_validation.get('final_decision', 'hold'),
                'decision_changed_by_risk_control': enhanced_result.get('decision', 'hold') != risk_validation.get('final_decision', 'hold'),
                'risk_rejection_reasons': risk_validation.get('risk_validation_result', {}).get('risk_validation', {}).get('rejection_reasons', []),
                'market_environment_score': risk_validation.get('market_environment_assessment', {}).get('overall_risk_level', 'medium')
            }

            # 合并摘要
            base_summary.update(risk_info)
            base_summary['summary_version'] = 'V2_with_risk_control'

            return base_summary

        except Exception as e:
            self.logger.error(f"协调摘要V2生成失败: {str(e)}")
            return {'error': str(e)}

    def _generate_coordination_summary(self, technical_data: Dict,
                                     gua_data: Dict,
                                     flow_data: Dict,
                                     enhanced_result: Dict) -> Dict[str, Any]:
        """生成协调摘要"""
        try:
            # 提取关键信息
            technical_signal = technical_data.get('buy_signal', False)
            gua_score = gua_data.get('gua_score', 0.0)
            flow_liquidity = flow_data.get('high_liquidity', False)
            enhanced_decision = enhanced_result.get('decision', 'hold')
            enhanced_confidence = enhanced_result.get('confidence', 0.0)
            
            # 信号一致性分析
            signals = []
            if technical_signal:
                signals.append('technical_buy')
            if gua_score > 0.3:
                signals.append('gua_positive')
            if flow_liquidity:
                signals.append('flow_positive')
            
            signal_consistency = len(signals) / 3  # 标准化到0-1
            
            # 风险评估
            risk_factors = []
            if not flow_liquidity:
                risk_factors.append('low_liquidity')
            if enhanced_confidence < 0.5:
                risk_factors.append('low_confidence')
            if gua_score < -0.3:
                risk_factors.append('negative_gua')
            
            risk_level = 'high' if len(risk_factors) >= 2 else 'medium' if len(risk_factors) == 1 else 'low'
            
            return {
                'signal_consistency': signal_consistency,
                'risk_level': risk_level,
                'risk_factors': risk_factors,
                'active_signals': signals,
                'final_decision': enhanced_decision,
                'decision_confidence': enhanced_confidence,
                'recommendation': self._generate_recommendation(enhanced_decision, enhanced_confidence, risk_level)
            }
            
        except Exception as e:
            self.logger.error(f"协调摘要生成失败: {str(e)}")
            return {'error': str(e)}
    
    def _generate_recommendation(self, decision: str, confidence: float, risk_level: str) -> str:
        """生成投资建议"""
        try:
            if decision == 'buy' and confidence >= 0.7 and risk_level == 'low':
                return "强烈建议买入"
            elif decision == 'buy' and confidence >= 0.5:
                return "建议买入"
            elif decision == 'sell' and confidence >= 0.7:
                return "建议卖出"
            elif decision == 'sell' and confidence >= 0.5:
                return "考虑卖出"
            elif risk_level == 'high':
                return "建议观望，风险较高"
            else:
                return "建议观望"
        except Exception:
            return "建议观望"
    
    def _record_coordination(self, result: Dict[str, Any]) -> None:
        """记录协调历史"""
        try:
            coordination_record = {
                'timestamp': datetime.now(),
                'fund_code': result.get('fund_code'),
                'decision': result.get('enhanced_decision', {}).get('decision'),
                'confidence': result.get('enhanced_decision', {}).get('confidence'),
                'processing_time': result.get('total_processing_time')
            }
            self.coordination_history.append(coordination_record)
        except Exception as e:
            self.logger.error(f"记录协调历史失败: {str(e)}")
    
    def get_coordination_stats(self) -> Dict[str, Any]:
        """获取协调统计信息"""
        try:
            if not self.coordination_history:
                return {'total_coordinations': 0}
            
            total_coordinations = len(self.coordination_history)
            avg_processing_time = sum(r.get('processing_time', 0) for r in self.coordination_history) / total_coordinations
            
            decisions = [r.get('decision') for r in self.coordination_history if r.get('decision')]
            decision_distribution = {
                'buy': decisions.count('buy'),
                'sell': decisions.count('sell'),
                'hold': decisions.count('hold')
            }
            
            return {
                'total_coordinations': total_coordinations,
                'avg_processing_time': avg_processing_time,
                'decision_distribution': decision_distribution,
                'last_coordination': self.coordination_history[-1]['timestamp'].isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"获取协调统计失败: {str(e)}")
            return {'error': str(e)}
