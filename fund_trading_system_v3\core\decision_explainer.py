"""
决策解释器
提供风控决策的详细解释和可视化分析
"""

import logging
import json
from datetime import datetime
from typing import Dict, Any, List, Optional
from dataclasses import asdict

from .data_structures import RiskControlResult, TechnicalIndicatorResult


class DecisionExplainer:
    """
    @class DecisionExplainer
    @brief 决策解释器
    @details 提供风控决策的详细解释和透明度
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.explanation_templates = self._load_explanation_templates()
        
    def _load_explanation_templates(self) -> Dict[str, str]:
        """加载解释模板"""
        return {
            'buy_approved': "✅ 买入条件验证通过",
            'buy_rejected_technical': "❌ 买入被拒绝 - 技术指标不满足条件",
            'buy_rejected_market': "❌ 买入被拒绝 - 市场环境不适宜",
            'buy_rejected_portfolio': "❌ 买入被拒绝 - 组合风险过高",
            'buy_delayed': "⏸️ 买入延迟 - 建议等待更好时机",
            'position_reduced': "📉 建议减少仓位 - 风险控制",
            
            'bollinger_violation': "布林线位置不符合买入要求",
            'rsi_violation': "RSI指标超出安全买入区间",
            'volume_violation': "成交量不足以支撑买入决策",
            'macd_warning': "MACD信号偏弱，建议谨慎",
            
            'high_volatility': "当前市场波动性过高",
            'poor_liquidity': "市场流动性不足",
            'negative_sentiment': "市场情绪过于悲观",
            
            'position_limit': "单仓位将超过风控限制",
            'sector_concentration': "行业集中度将过高",
            'correlation_risk': "与现有持仓相关性过高"
        }
    
    def explain_risk_decision(self, risk_result: RiskControlResult,
                            technical_details: Dict[str, Any] = None,
                            market_details: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        @brief 解释风控决策
        @param risk_result: 风控验证结果
        @param technical_details: 技术分析详情
        @param market_details: 市场分析详情
        @return: 详细解释结果
        """
        try:
            explanation = {
                'fund_code': risk_result.fund_code,
                'decision_time': risk_result.validation_time.isoformat(),
                'final_decision': risk_result.recommended_action,
                'risk_level': risk_result.risk_level,
                'overall_summary': self._generate_overall_summary(risk_result),
                'detailed_analysis': self._generate_detailed_analysis(risk_result, technical_details, market_details),
                'risk_factors': self._analyze_risk_factors(risk_result),
                'recommendations': self._generate_recommendations(risk_result),
                'confidence_analysis': self._analyze_confidence(risk_result),
                'visual_indicators': self._generate_visual_indicators(risk_result, technical_details)
            }
            
            return explanation
            
        except Exception as e:
            self.logger.error(f"决策解释生成失败: {str(e)}")
            return {
                'fund_code': risk_result.fund_code,
                'error': str(e),
                'summary': '决策解释生成失败，请查看日志'
            }
    
    def _generate_overall_summary(self, risk_result: RiskControlResult) -> str:
        """生成总体摘要"""
        if risk_result.passed:
            return f"✅ {risk_result.fund_code} 通过风控验证，建议{risk_result.recommended_action}，风险等级：{risk_result.risk_level}"
        else:
            reasons = "; ".join(risk_result.rejection_reasons[:3])  # 只显示前3个原因
            return f"❌ {risk_result.fund_code} 未通过风控验证，主要原因：{reasons}"
    
    def _generate_detailed_analysis(self, risk_result: RiskControlResult,
                                   technical_details: Dict[str, Any] = None,
                                   market_details: Dict[str, Any] = None) -> Dict[str, Any]:
        """生成详细分析"""
        analysis = {
            'technical_analysis': self._explain_technical_violations(risk_result.technical_violations),
            'market_analysis': self._explain_market_environment(risk_result.market_environment_score, market_details),
            'portfolio_analysis': self._explain_portfolio_risk(risk_result.portfolio_risk_score),
            'scoring_breakdown': {
                'technical_score': 1.0 - len(risk_result.technical_violations) * 0.3,
                'market_score': risk_result.market_environment_score,
                'portfolio_score': risk_result.portfolio_risk_score,
                'composite_score': risk_result.confidence
            }
        }
        
        # 添加技术指标详细分析
        if technical_details:
            analysis['technical_indicators_detail'] = self._explain_technical_indicators(technical_details)
        
        return analysis
    
    def _explain_technical_violations(self, violations: Dict[str, Any]) -> Dict[str, Any]:
        """解释技术指标违规"""
        explanations = {}
        
        for indicator, violation in violations.items():
            if indicator == 'bollinger_bands':
                explanations[indicator] = {
                    'violation': violation,
                    'explanation': self.explanation_templates['bollinger_violation'],
                    'impact': '高',
                    'suggestion': '等待价格回落至布林线下轨附近再考虑买入'
                }
            elif indicator == 'rsi':
                explanations[indicator] = {
                    'violation': violation,
                    'explanation': self.explanation_templates['rsi_violation'],
                    'impact': '中',
                    'suggestion': '等待RSI回落至65以下再考虑买入'
                }
            elif indicator == 'volume':
                explanations[indicator] = {
                    'violation': violation,
                    'explanation': self.explanation_templates['volume_violation'],
                    'impact': '中',
                    'suggestion': '等待成交量放大确认买入信号'
                }
        
        return explanations
    
    def _explain_market_environment(self, market_score: float, market_details: Dict[str, Any] = None) -> Dict[str, Any]:
        """解释市场环境"""
        if market_score > 0.7:
            assessment = "市场环境良好，适合买入"
            risk_level = "低"
        elif market_score > 0.4:
            assessment = "市场环境一般，需谨慎操作"
            risk_level = "中"
        else:
            assessment = "市场环境较差，建议观望"
            risk_level = "高"
        
        explanation = {
            'score': market_score,
            'assessment': assessment,
            'risk_level': risk_level,
            'factors': []
        }
        
        if market_details:
            # 分析具体的市场因素
            if market_details.get('volatility_score', 0.5) > 0.7:
                explanation['factors'].append("市场波动性较高")
            if market_details.get('liquidity_score', 0.5) < 0.3:
                explanation['factors'].append("流动性不足")
            if market_details.get('sentiment_score', 0.5) < 0.3:
                explanation['factors'].append("市场情绪悲观")
        
        return explanation
    
    def _explain_portfolio_risk(self, portfolio_score: float) -> Dict[str, Any]:
        """解释组合风险"""
        if portfolio_score > 0.7:
            assessment = "组合风险控制良好"
            risk_level = "低"
        elif portfolio_score > 0.4:
            assessment = "组合风险适中，需注意分散化"
            risk_level = "中"
        else:
            assessment = "组合风险较高，建议调整仓位结构"
            risk_level = "高"
        
        return {
            'score': portfolio_score,
            'assessment': assessment,
            'risk_level': risk_level,
            'suggestions': self._get_portfolio_suggestions(portfolio_score)
        }
    
    def _get_portfolio_suggestions(self, score: float) -> List[str]:
        """获取组合建议"""
        suggestions = []
        
        if score < 0.4:
            suggestions.extend([
                "考虑减少单一仓位规模",
                "增加持仓分散化",
                "关注行业集中度风险"
            ])
        elif score < 0.7:
            suggestions.extend([
                "保持当前分散化水平",
                "定期检查相关性风险"
            ])
        else:
            suggestions.append("当前组合结构良好，可考虑适度增加仓位")
        
        return suggestions
    
    def _explain_technical_indicators(self, technical_details: Dict[str, Any]) -> Dict[str, Any]:
        """解释技术指标详情"""
        explanations = {}
        
        # 布林线分析
        if 'bollinger_analysis' in technical_details:
            bb_data = technical_details['bollinger_analysis']
            explanations['bollinger_bands'] = {
                'current_position': f"{bb_data.get('relative_position', 0):.1%}",
                'interpretation': self._interpret_bb_position(bb_data.get('relative_position', 0.5)),
                'recommendation': self._get_bb_recommendation(bb_data.get('relative_position', 0.5))
            }
        
        # RSI分析
        if 'rsi_analysis' in technical_details:
            rsi_data = technical_details['rsi_analysis']
            explanations['rsi'] = {
                'current_value': rsi_data.get('rsi_value', 50),
                'level': rsi_data.get('rsi_level', '中性'),
                'interpretation': self._interpret_rsi_level(rsi_data.get('rsi_value', 50)),
                'recommendation': self._get_rsi_recommendation(rsi_data.get('rsi_value', 50))
            }
        
        # 成交量分析
        if 'volume_analysis' in technical_details:
            vol_data = technical_details['volume_analysis']
            explanations['volume'] = {
                'volume_ratio': vol_data.get('volume_ratio', 1.0),
                'level': vol_data.get('volume_level', '正常'),
                'interpretation': self._interpret_volume_level(vol_data.get('volume_ratio', 1.0)),
                'recommendation': self._get_volume_recommendation(vol_data.get('volume_ratio', 1.0))
            }
        
        return explanations
    
    def _interpret_bb_position(self, position: float) -> str:
        """解释布林线位置"""
        if position < 0.2:
            return "价格位于布林线下轨附近，可能存在超卖机会"
        elif position < 0.4:
            return "价格位于布林线下半部分，相对安全的买入区域"
        elif position < 0.6:
            return "价格位于布林线中部，处于中性区域"
        elif position < 0.8:
            return "价格位于布林线上半部分，需谨慎买入"
        else:
            return "价格位于布林线上轨附近，存在超买风险"
    
    def _get_bb_recommendation(self, position: float) -> str:
        """获取布林线建议"""
        if position <= 0.2:
            return "符合买入条件，可以考虑建仓"
        elif position <= 0.4:
            return "接近买入条件，可适量建仓"
        else:
            return "不符合买入条件，建议等待价格回落"
    
    def _interpret_rsi_level(self, rsi: float) -> str:
        """解释RSI水平"""
        if rsi < 30:
            return "RSI显示超卖状态，可能存在反弹机会"
        elif rsi < 50:
            return "RSI处于偏弱区域，但未达到超卖"
        elif rsi < 70:
            return "RSI处于正常区域"
        else:
            return "RSI显示超买状态，存在回调风险"
    
    def _get_rsi_recommendation(self, rsi: float) -> str:
        """获取RSI建议"""
        if rsi <= 65:
            return "RSI水平适合买入"
        else:
            return "RSI过高，建议等待回调"
    
    def _interpret_volume_level(self, ratio: float) -> str:
        """解释成交量水平"""
        if ratio < 0.8:
            return "成交量偏低，市场参与度不高"
        elif ratio < 1.2:
            return "成交量正常"
        elif ratio < 2.0:
            return "成交量活跃，市场关注度较高"
        else:
            return "成交量异常放大，需关注异动原因"
    
    def _get_volume_recommendation(self, ratio: float) -> str:
        """获取成交量建议"""
        if ratio >= 1.2:
            return "成交量充足，支持买入决策"
        else:
            return "成交量不足，建议等待放量确认"
    
    def _analyze_risk_factors(self, risk_result: RiskControlResult) -> List[Dict[str, Any]]:
        """分析风险因素"""
        risk_factors = []
        
        # 技术风险
        if risk_result.technical_violations:
            risk_factors.append({
                'category': '技术指标风险',
                'level': 'high' if len(risk_result.technical_violations) > 2 else 'medium',
                'description': f"有{len(risk_result.technical_violations)}个技术指标不满足买入条件",
                'details': list(risk_result.technical_violations.keys())
            })
        
        # 市场环境风险
        if risk_result.market_environment_score < 0.4:
            risk_factors.append({
                'category': '市场环境风险',
                'level': 'high' if risk_result.market_environment_score < 0.2 else 'medium',
                'description': '当前市场环境不利于买入操作',
                'details': ['市场波动性', '流动性', '情绪指标']
            })
        
        # 组合风险
        if risk_result.portfolio_risk_score < 0.4:
            risk_factors.append({
                'category': '组合风险',
                'level': 'high' if risk_result.portfolio_risk_score < 0.2 else 'medium',
                'description': '投资组合风险控制需要关注',
                'details': ['仓位集中度', '行业分散度', '相关性风险']
            })
        
        return risk_factors
    
    def _generate_recommendations(self, risk_result: RiskControlResult) -> List[str]:
        """生成操作建议"""
        recommendations = []
        
        if risk_result.passed:
            recommendations.append(f"建议{risk_result.recommended_action}，但需持续监控风险指标")
            if risk_result.confidence < 0.8:
                recommendations.append("置信度不高，建议适当控制仓位规模")
        else:
            recommendations.append("当前不建议买入，建议等待以下条件改善：")
            recommendations.extend(risk_result.rejection_reasons[:3])
        
        return recommendations
    
    def _analyze_confidence(self, risk_result: RiskControlResult) -> Dict[str, Any]:
        """分析置信度"""
        confidence = risk_result.confidence
        
        if confidence > 0.8:
            level = "高"
            description = "风控验证结果可信度高"
        elif confidence > 0.6:
            level = "中"
            description = "风控验证结果可信度中等"
        else:
            level = "低"
            description = "风控验证结果可信度较低，建议谨慎操作"
        
        return {
            'score': confidence,
            'level': level,
            'description': description,
            'factors': self._get_confidence_factors(risk_result)
        }
    
    def _get_confidence_factors(self, risk_result: RiskControlResult) -> List[str]:
        """获取影响置信度的因素"""
        factors = []
        
        if risk_result.technical_violations:
            factors.append("技术指标存在违规")
        if risk_result.market_environment_score < 0.5:
            factors.append("市场环境评分偏低")
        if risk_result.portfolio_risk_score < 0.5:
            factors.append("组合风险评分偏低")
        
        return factors if factors else ["各项指标均正常"]
    
    def _generate_visual_indicators(self, risk_result: RiskControlResult,
                                   technical_details: Dict[str, Any] = None) -> Dict[str, Any]:
        """生成可视化指标"""
        indicators = {
            'risk_gauge': {
                'value': 1.0 - risk_result.confidence,
                'level': risk_result.risk_level,
                'color': self._get_risk_color(risk_result.risk_level)
            },
            'technical_radar': self._generate_technical_radar(technical_details),
            'decision_flow': self._generate_decision_flow(risk_result)
        }
        
        return indicators
    
    def _get_risk_color(self, risk_level: str) -> str:
        """获取风险等级对应的颜色"""
        colors = {
            'low': 'green',
            'medium': 'yellow',
            'high': 'orange',
            'critical': 'red'
        }
        return colors.get(risk_level, 'gray')
    
    def _generate_technical_radar(self, technical_details: Dict[str, Any] = None) -> Dict[str, float]:
        """生成技术指标雷达图数据"""
        if not technical_details:
            return {}
        
        radar_data = {}
        
        if 'bollinger_analysis' in technical_details:
            radar_data['布林线'] = technical_details['bollinger_analysis'].get('score', 0.5)
        if 'rsi_analysis' in technical_details:
            radar_data['RSI'] = technical_details['rsi_analysis'].get('score', 0.5)
        if 'volume_analysis' in technical_details:
            radar_data['成交量'] = technical_details['volume_analysis'].get('score', 0.5)
        if 'macd_analysis' in technical_details:
            radar_data['MACD'] = technical_details['macd_analysis'].get('score', 0.5)
        
        return radar_data
    
    def _generate_decision_flow(self, risk_result: RiskControlResult) -> List[Dict[str, Any]]:
        """生成决策流程"""
        flow = [
            {
                'step': '技术指标验证',
                'status': 'passed' if not risk_result.technical_violations else 'failed',
                'details': f"检查了{len(risk_result.technical_violations) + 3}个指标"
            },
            {
                'step': '市场环境评估',
                'status': 'passed' if risk_result.market_environment_score > 0.5 else 'warning',
                'details': f"评分: {risk_result.market_environment_score:.2f}"
            },
            {
                'step': '组合风险评估',
                'status': 'passed' if risk_result.portfolio_risk_score > 0.5 else 'warning',
                'details': f"评分: {risk_result.portfolio_risk_score:.2f}"
            },
            {
                'step': '最终决策',
                'status': 'passed' if risk_result.passed else 'failed',
                'details': f"建议: {risk_result.recommended_action}"
            }
        ]
        
        return flow
